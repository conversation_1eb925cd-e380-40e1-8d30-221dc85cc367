[package]
name = "rust-bypassAV-simple"
version = "0.1.0"
edition = "2021"

[dependencies]
# 最小化依赖 - 只保留必要的Windows API
winapi = {version = "0.3.9", features = [
    "winnt",
    "winbase",
    "memoryapi",
    "errhandlingapi"
]}

[profile.release]
# 简化的编译配置
panic = "abort"
lto = false  # 关闭链接时优化
incremental = false
codegen-units = 16  # 增加编译单元数量
opt-level = "2"  # 降低优化级别
debug = false
strip = true  # 移除调试符号
