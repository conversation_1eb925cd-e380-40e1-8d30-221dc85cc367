{"rustc": 8024708092284749966, "features": "[\"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 12879576032848453124, "deps": [[40386456601120721, "percent_encoding", false, 18356311257665602821], [95042085696191081, "ipnet", false, 16058963219010579529], [418947936956741439, "h2", false, 5569003682266624791], [784494742817713399, "tower_service", false, 1849519588033901642], [1811549171721445101, "futures_channel", false, 10830539679627718888], [1906322745568073236, "pin_project_lite", false, 196626964191281353], [2517136641825875337, "sync_wrapper", false, 15576737340315027687], [3150220818285335163, "url", false, 1073900394536136371], [3722963349756955755, "once_cell", false, 10591124939942395271], [5340155882212614564, "hyper_util", false, 1097379868465445239], [5695049318159433696, "tower", false, 16413657763735974128], [5986029879202738730, "log", false, 12843816569325109688], [7303982924001358866, "tokio", false, 10050774766292549764], [7620660491849607393, "futures_core", false, 15290116935053123842], [9010263965687315507, "http", false, 7961737800305010567], [9689903380558560274, "serde", false, 12526162590250596294], [10229185211513642314, "mime", false, 7386896582578899558], [10629569228670356391, "futures_util", false, 17473155703654275401], [11957360342995674422, "hyper", false, 11866637400306049493], [12186126227181294540, "tokio_native_tls", false, 5200311233716528956], [13077212702700853852, "base64", false, 559768677435555661], [14084095096285906100, "http_body", false, 2048407752182475111], [14564311161534545801, "encoding_rs", false, 10852611864949047025], [15032952994102373905, "rustls_pemfile", false, 2475113796139074201], [15367738274754116744, "serde_json", false, 1949801368163025085], [15697835491348449269, "windows_registry", false, 8559938241831075831], [16066129441945555748, "bytes", false, 15035161482080564528], [16542808166767769916, "serde_urlencoded", false, 13193955071199828972], [16785601910559813697, "native_tls_crate", false, 12178500344912986844], [16900715236047033623, "http_body_util", false, 2397881298046652264], [18273243456331255970, "hyper_tls", false, 17157126470654650454]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-483d4dd1bf834ce1\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}