# PowerShell script to build simplified bypass program
Write-Host "Building simplified bypass program..." -ForegroundColor Green

# Backup original files
if (Test-Path "src\main_backup.rs") {
    Write-Host "Backup file already exists" -ForegroundColor Yellow
} else {
    Copy-Item "src\main.rs" "src\main_backup.rs"
    Write-Host "Backed up original main.rs" -ForegroundColor Green
}

if (Test-Path "Cargo_backup.toml") {
    Write-Host "Backup file already exists" -ForegroundColor Yellow
} else {
    Copy-Item "Cargo.toml" "Cargo_backup.toml"
    Write-Host "Backed up original Cargo.toml" -ForegroundColor Green
}

# Use simplified version
Copy-Item "src\main_simple.rs" "src\main.rs" -Force
Copy-Item "Cargo_simple.toml" "Cargo.toml" -Force

Write-Host "Cleaning previous build..." -ForegroundColor Blue
cargo clean

Write-Host "Building simplified version..." -ForegroundColor Blue
cargo build --release

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "Output file: target\release\rust-bypassAV-simple.exe" -ForegroundColor Green
    
    # Generate random filename
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $outputName = "SimpleBypass_$timestamp.exe"
    
    Copy-Item "target\release\rust-bypassAV-simple.exe" $outputName
    Write-Host "Copied to: $outputName" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "Please test this simplified version for AV evasion" -ForegroundColor Cyan
    Write-Host "If successful, we can gradually add features" -ForegroundColor Cyan
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
