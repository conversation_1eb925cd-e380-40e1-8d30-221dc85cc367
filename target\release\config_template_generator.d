C:\Users\<USER>\Desktop\rust_bypass1\target\release\config_template_generator.exe: C:\Users\<USER>\Desktop\rust_bypass1\beacon.bin C:\Users\<USER>\Desktop\rust_bypass1\build.rs C:\Users\<USER>\Desktop\rust_bypass1\config.json C:\Users\<USER>\Desktop\rust_bypass1\shellcode_encrypted.txt C:\Users\<USER>\Desktop\rust_bypass1\src\config\defense_config.rs C:\Users\<USER>\Desktop\rust_bypass1\src\config\execution_config.rs C:\Users\<USER>\Desktop\rust_bypass1\src\config\loader.rs C:\Users\<USER>\Desktop\rust_bypass1\src\config\memory_config.rs C:\Users\<USER>\Desktop\rust_bypass1\src\config\mod.rs C:\Users\<USER>\Desktop\rust_bypass1\src\config\sandbox_config.rs C:\Users\<USER>\Desktop\rust_bypass1\src\constants.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\crypto.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\embedded.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\encrypted_memory.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\enhanced_encryption.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\execution.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\memory.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\mod.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\module_stomping.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\shellcode_loader.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\stealth_memory.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\syscalls.rs C:\Users\<USER>\Desktop\rust_bypass1\src\core\thread_hijack.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\amsi.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\cmdline.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\enhanced_bypass.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\etw.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\evasion_manager.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\identity.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\mod.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\ppid.rs C:\Users\<USER>\Desktop\rust_bypass1\src\defense\sandbox.rs C:\Users\<USER>\Desktop\rust_bypass1\src\error\mod.rs C:\Users\<USER>\Desktop\rust_bypass1\src\lib.rs C:\Users\<USER>\Desktop\rust_bypass1\src\orchestrator.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\code_cave_scanner.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\logger.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\mod.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\pe_parser.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\process_manager.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\random.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\stealth_techniques.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\winapi.rs C:\Users\<USER>\Desktop\rust_bypass1\src\utils\winapi_utils.rs C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out\embedded_config.json C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out\embedded_shellcode.rs C:\Users\<USER>\Desktop\rust_bypass1\tools\config_template_generator.rs
