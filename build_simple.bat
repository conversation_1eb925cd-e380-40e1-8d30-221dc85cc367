@echo off
echo 构建简化版本的免杀程序...

REM 备份原始文件
if exist src\main_backup.rs (
    echo 备份文件已存在
) else (
    copy src\main.rs src\main_backup.rs
    echo 已备份原始main.rs
)

if exist Cargo_backup.toml (
    echo 备份文件已存在
) else (
    copy Cargo.toml Cargo_backup.toml
    echo 已备份原始Cargo.toml
)

REM 使用简化版本
copy src\main_simple.rs src\main.rs
copy Cargo_simple.toml Cargo.toml

echo 清理之前的构建...
cargo clean

echo 构建简化版本...
cargo build --release

if %ERRORLEVEL% EQU 0 (
    echo 构建成功！
    echo 输出文件: target\release\rust-bypassAV-simple.exe
    
    REM 生成随机文件名
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
    set "datestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"
    
    copy target\release\rust-bypassAV-simple.exe "SimpleBypass_%datestamp%.exe"
    echo 已复制到: SimpleBypass_%datestamp%.exe
    
    echo.
    echo 请测试此简化版本的免杀效果
    echo 如果成功，我们可以逐步添加功能
) else (
    echo 构建失败！
)

pause
