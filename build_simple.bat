@echo off
echo Building simplified bypass program...

REM Backup original files
if exist src\main_backup.rs (
    echo Backup file already exists
) else (
    copy src\main.rs src\main_backup.rs
    echo Backed up original main.rs
)

if exist Cargo_backup.toml (
    echo Backup file already exists
) else (
    copy Cargo.toml Cargo_backup.toml
    echo Backed up original Cargo.toml
)

REM Use simplified version
copy src\main_simple.rs src\main.rs
copy Cargo_simple.toml Cargo.toml

echo Cleaning previous build...
cargo clean

echo Building simplified version...
cargo build --release

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Output file: target\release\rust-bypassAV-simple.exe

    REM Generate random filename
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
    set "datestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

    copy target\release\rust-bypassAV-simple.exe "SimpleBypass_%datestamp%.exe"
    echo Copied to: SimpleBypass_%datestamp%.exe

    echo.
    echo Please test this simplified version for AV evasion
    echo If successful we can gradually add features
) else (
    echo Build failed!
)

pause
